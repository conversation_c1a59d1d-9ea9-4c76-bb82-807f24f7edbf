# 🎥 OBS Video Hub

**Hub de communication centralisé pour OBS Studio, Gabin et Companion**

OBS Video Hub est une application Node.js/NestJS qui fait le pont entre différents outils de production vidéo :
- **OBS Studio** : Logiciel de streaming/enregistrement ([OBS Studio](https://obsproject.com/))
- **Companion** : Contrôleur de surfaces de contrôle ([Bitfocus Companion](https://bitfocus.io/companion))
- **Gabin** : Système de caméras automatiques ([Fork SUN](https://gitlab.lesonunique.com/vleveque/gabin-fork) de [one-click-studio/gabin](https://github.com/one-click-studio/gabin))

## 🚀 Démarrage Rapide

```bash
# 1. Installation
npm install

# 2. Premier démarrage (génère les configurations)
npm run start

# 3. Activez les modules souhaités dans src/config/custom/modules.custom.ts
# 4. Configurez OBS dans src/config/custom/obs.custom.ts

# 5. Démarrage normal
npm run start:dev
```

> 💡 **Conseil :** Commencez par activer uniquement le module OBS pour tester la connexion

## 🏗️ Architecture

L'application utilise une **architecture modulaire autonome** avec un Hub central d'orchestration :

```
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│     OBS     │    │ Companion   │    │   Gabin     │
│   Module    │    │   Module    │    │   Module    │
│             │    │             │    │             │
│ • Audio     │    │ • Actions   │    │ • Autocam   │
│ • Caméras   │    │ • Feedbacks │    │ • Micros    │
│ • WebSocket │    │ • OSC       │    │ • OSC       │
└─────────────┘    └─────────────┘    └─────────────┘
       │                   │                   │
       └───────────────────┼───────────────────┘
                           │
                    ┌─────────────┐
                    │ Hub Central │
                    │             │
                    │ • Liens     │
                    │ • Status    │
                    │ • API       │
                    └─────────────┘
```

### 🧩 Modules autonomes
- **OBS** : WebSocket, contrôle audio/vidéo, automation caméras
- **Companion** : OSC, actions/feedbacks pour surfaces de contrôle
- **Gabin** : Communication OSC, gestion autocam/micros
- **LowerThird** : Interface popup pour bandeaux titre avec NodeCG (utilisation avec le projet fork [NodeCG](https://gitlab.lesonunique.com/vleveque/nodecg_sun) )
- **DjAutoCamera** : Automation de changements de caméras aléatoires pour les mix DJ
- **Hub** : Orchestration pure, liens entre modules

## 🚀 Installation et Premier Démarrage

### 1. Installation des dépendances

```bash
npm install
```

### 2. Premier démarrage (génération des configurations)

```bash
npm run start
```

**⚠️ Important :** Au premier démarrage, l'application va :
1. Générer automatiquement les fichiers de configuration personnalisés
2. S'arrêter avec un message explicatif
3. Vous demander de personnaliser les configurations si nécessaire

### 3. Configuration des modules

Après le premier démarrage, activez les modules souhaités dans `src/config/custom/modules.custom.ts` :

```typescript
export const modulesCustomConfig: ModulesConfig = {
    gabin: { enabled: false },      // Gabin (optionnel)
    obs: { enabled: true },         // OBS Studio (recommandé)
    companion: { enabled: true },   // Companion (optionnel)
};
```

Puis personnalisez les configurations spécifiques :
- **`src/config/custom/obs.custom.ts`** : Configuration OBS (host, port, password)
- **`src/config/custom/companion.custom.ts`** : Configuration Companion (feedbacks, actions)
- **`src/config/custom/audio-to-camera.custom.ts`** : Automation caméras basée sur l'audio
- **`src/config/custom/dj-auto-camera.custom.ts`** : Automation caméras aléatoires pour les mix DJ

### 4. Démarrage normal

```bash
# Mode développement
npm run start:dev

# Mode production
npm run start:prod
```

## 🔧 Configuration

### Structure de configuration

L'application utilise un système de configuration personnalisé :

```
src/config/
├── app.config.ts                   # Config générale (HTTP_PORT, NODE_ENV)
├── custom/                         # 🔧 FICHIERS ÉDITABLES (ignorés par Git)
│   ├── modules.custom.ts           # Activation/désactivation des modules
│   ├── obs.custom.ts               # Configuration OBS WebSocket
│   ├── companion.custom.ts         # Configuration Companion OSC
│   ├── gabin.custom.ts             # Configuration Gabin (si activé)
│   ├── audio-to-camera.custom.ts   # Automation caméras
│   ├── dj-auto-camera.custom.ts    # Automation caméras DJ
│   └── lowerthird.custom.ts        # Configuration Lower Third
├── config.generator.ts             # Génération automatique
└── index.ts                        # Chargement direct
```

### Variables d'environnement (.env)

Créez un fichier `.env` pour la configuration générale :

```bash
# Configuration générale
HTTP_PORT=3000
NODE_ENV=development
```

### Configuration OBS (obs.custom.ts)

```typescript
export const obsCustomConfig: OBSConfig = {
    network: {
        host: '127.0.0.1',
        port: 4455,
        password: 'votre_mot_de_passe',  // Configuré dans OBS
    },
    autoConnect: true,
};
```

### Configuration Automation Caméras (audio-to-camera.custom.ts)

```typescript
export const audioToCameraCustomConfig: AudioToCameraConfig = {
    enabled: true,
    targetScene: 'AUTO CAM',
    audioThresholdDb: -40,              // Seuil de déclenchement des micros
    activationDelayMs: 100,             // Délai d'activation (première activation)
    cameraMinHoldMs: 4000,              // Protection minimum (caméras et plan large)

    micToCameraMapping: [
        { micName: 'mic_invite1', cameraSource: 'CAM 2' },
        { micName: 'mic_invite2', cameraSource: 'CAM 3' },
    ],

    forceFullscreenSources: [
        // Sources audio qui forcent le mode fullscreen (plan large)
        // 'audio_musique',     // Source audio externe
    ],

    fallbackBehavior: {
        hideAllWhenInactive: true,      // Masquer toutes les caméras quand aucun micro actif
    },

    cameraTransition: {
        useOpacityFilter: true,         // Utiliser l'opacité pour des transitions fluides
        filterName: 'Opacity',         // Nom du filtre d'opacité dans OBS
    },

    fullscreenMode: {
        enabled: true,                  // Mode plein écran quand plusieurs caméras actives
        minActiveMics: 2,               // Nombre minimum de caméras pour déclencher
    },

    audioStabilization: {
        enabled: false,                 // Stabilisation contre les fluctuations audio
        fluctuationWindowMs: 2000,
        minStableDurationMs: 500,
    },

    debug: {
        verbose: true,                  // Logs détaillés pour le debug
        logAudioLevels: false,
        logAudioThresholds: false,
        audioLogIntervalMs: 5000,
    },
};
```

## 🌐 API

### Status API

**GET** `http://localhost:3000/status`

Retourne l'état complet du système (modules connectés, liens actifs, etc.)

## 🎛️ Fonctionnalités

### 🎥 Automation Caméras
- **Changement automatique** basé sur les niveaux audio des micros
- **Protection temporelle** : Maintien minimum de 4s pour chaque caméra et plan large
- **Mode plein écran** : Activation automatique quand plusieurs caméras sont actives
- **Force Fullscreen** : Sources audio spéciales qui forcent le mode plein écran (musique, animateur, etc.)
- **Stabilisation audio** : Protection contre les fluctuations rapides des micros
- **Transitions fluides** : Support des filtres d'opacité OBS pour éviter les frames anciennes
- **Configuration flexible** : Mappings micro → caméra personnalisables
- **Debug avancé** : Logs détaillés pour ajuster les seuils et temporisations

### 🎛️ Contrôle Companion
- Actions OSC pour contrôler OBS depuis les surfaces de contrôle
- Feedbacks automatiques vers Companion
- Support des boutons toggle, on/off

### 🔗 Intégration OBS
- Connexion WebSocket vers OBS Studio
- Contrôle des sources audio/vidéo
- Monitoring des niveaux audio en temps réel

## 🎯 Force Fullscreen

La fonctionnalité **Force Fullscreen** permet de définir des sources audio spéciales qui, lorsqu'elles sont actives, forcent automatiquement le passage en mode plein écran (plan large), indépendamment de l'état des autres microphones.

### 📋 Cas d'usage
- **Microphone de présentation** : Quand quelqu'un fait une présentation, afficher le plan large
- **Microphone d'animateur** : Quand l'animateur parle, forcer le plan large
- **Source audio externe** : Musique, vidéo, ou autre contenu nécessitant un affichage fullscreen

### ⚙️ Configuration

Dans `audio-to-camera.custom.ts` :

```typescript
forceFullscreenSources: [
    'audio_musique',        // Source audio musique
    'mic_animateur',        // Microphone d'animateur
    'mic_presentation',     // Microphone de présentation
],
```

### 🔄 Comportement
1. **Priorité absolue** : Quand une source de `forceFullscreenSources` est active → mode fullscreen immédiat
2. **Respect des délais** : Tous les délais configurés (`cameraMinHoldMs`, `activationDelayMs`) sont respectés
3. **Stabilisation** : La stabilisation audio s'applique aussi à ces sources
4. **Logging** : Messages "Force fullscreen triggered by: ..." si debug verbose activé

### 📺 Lower Third (Bandeaux Titre)
- Interface popup Electron pour saisie des titres/sous-titres
- Intégration avec NodeCG pour l'affichage des bandeaux
- Configuration flexible des URLs et ports
- API REST pour récupération et mise à jour des données

**Configuration** (`lowerthird.custom.ts`) :
```typescript
export const lowerThirdCustomConfig: LowerThirdConfig = {
    nodeCG: {
        host: 'localhost',
        port: 9090,                    // Port de votre service NodeCG
        basePath: '/api/lowerthird'    // Chemin de base de l'API
    },
    popupElectron: {
        host: 'localhost',
        port: 3210,                    // Port de la popup Electron
        path: '/open'                  // Endpoint d'ouverture
    }
};
```

**Utilisation** :
- Accédez à `http://localhost:3000/lowerthird/popup` pour ouvrir la popup
- La popup récupère automatiquement les données actuelles depuis NodeCG
- Après confirmation, les nouvelles données sont envoyées à NodeCG

### 🎵 DJ Auto Camera (Changements Automatiques)
- Automation de changements de caméras aléatoires pour les mix DJ
- Intervalles de changement configurables (min/max)
- Mode intro avec caméra principale fixe
- Détection de scène pour activation/désactivation automatique
- Support des scènes fantômes (INTRO, etc.)
- Transitions fluides avec filtres d'opacité OBS
- Contrôle via Companion pour déclencher l'intro manuellement

**Configuration** (`dj-auto-camera.custom.ts`) :
```typescript
export const djAutoCameraCustomConfig: DjAutoCameraConfig = {
    enabled: false,
    targetScene: 'DJ AUTO CAM',           // Scène OBS à contrôler
    cameraSources: ['DJ CAM 1', 'DJ CAM 2', 'DJ CAM 3'],

    timing: {
        minSwitchIntervalMs: 6000,        // 6 secondes minimum entre changements
        maxSwitchIntervalMs: 25000,       // 25 secondes maximum
    },

    intro: {
        enabled: true,
        mainCamera: 'DJ CAM 1',           // Caméra fixe pendant l'intro
        durationMs: 25000,                // Durée de l'intro (25s)
    },

    sceneDetection: {
        enabled: true,
        onlyWhenSceneActive: true,        // Ne fonctionne que sur la scène cible
        ghostScenes: ['INTRO'],           // Scènes qui n'affectent pas l'état
    },

    cameraTransition: {
        useOpacityFilter: true,           // Transitions fluides avec opacité
        filterName: 'Opacity',
    },

    debug: {
        verbose: false,
        logSwitches: true,                // Logger chaque changement
    },
};
```

**Utilisation** :
- L'automation démarre automatiquement quand la scène `DJ AUTO CAM` est active
- Utilisez Companion pour déclencher l'intro manuellement
- Les changements se font à intervalles aléatoires entre min/max configurés

## 🐛 Dépannage

### OBS ne se connecte pas
1. Vérifiez qu'OBS Studio est démarré
2. Activez le WebSocket dans OBS : `Outils > obs-websocket Settings`
3. Vérifiez les paramètres dans `obs.custom.ts`

### Companion ne reçoit pas les feedbacks
1. Vérifiez la configuration dans `companion.custom.ts`
2. Envoyez `/companion/ready` depuis Companion
3. Vérifiez que les ports OSC sont corrects

### L'automation caméras ne fonctionne pas
1. **Activez le module** dans `modules.custom.ts`
2. **Vérifiez les noms des sources audio** dans `audio-to-camera.custom.ts`
3. **Ajustez les seuils audio** selon votre environnement (`audioThresholdDb`)
4. **Activez le debug** (`verbose: true`) pour voir les logs détaillés
5. **Vérifiez la scène cible** (`targetScene`) existe dans OBS
6. **Testez les noms des caméras** (`cameraSource`) correspondent aux sources OBS

### Les caméras changent trop rapidement
1. **Augmentez `cameraMinHoldMs`** (ex: 6000ms au lieu de 4000ms)
2. **Activez la stabilisation audio** (`audioStabilization.enabled: true`)
3. **Ajustez le seuil audio** pour éviter les faux déclenchements

### Le plan large ne reste pas assez longtemps
- Le plan large est maintenant protégé pendant `cameraMinHoldMs` comme les caméras
- Augmentez cette valeur si nécessaire (ex: 6000ms)

### La fonctionnalité Force Fullscreen ne fonctionne pas
1. **Vérifiez la configuration** : Les sources dans `forceFullscreenSources` doivent exister dans OBS
2. **Activez le debug** : `verbose: true` pour voir les messages "Force fullscreen triggered by: ..."
3. **Vérifiez les noms** : Les noms des sources doivent correspondre exactement à ceux d'OBS
4. **Testez les seuils** : Ajustez `audioThresholdDb` si les sources ne sont pas détectées

### Le DJ Auto Camera ne fonctionne pas
1. **Activez le module** dans `modules.custom.ts` (si un module DJ existe)
2. **Vérifiez la scène cible** : La scène `DJ AUTO CAM` doit exister dans OBS
3. **Vérifiez les noms des caméras** : Les sources dans `cameraSources` doivent correspondre aux sources OBS
4. **Activez le debug** : `verbose: true` et `logSwitches: true` pour voir les logs
5. **Testez la détection de scène** : Désactivez `sceneDetection.enabled` pour tester sans contrainte de scène

### Les caméras DJ changent trop/pas assez souvent
1. **Ajustez les intervalles** : Modifiez `minSwitchIntervalMs` et `maxSwitchIntervalMs`
2. **Vérifiez la scène active** : L'automation ne fonctionne que sur la scène cible
3. **Testez l'intro** : L'intro bloque les changements pendant sa durée

## 🛠️ Développement

### Scripts disponibles

```bash
# Développement avec rechargement automatique
npm run start:dev

# Build de production
npm run build

# Tests
npm run test

# Linting
npm run lint
```

## 📄 License

MIT License - Voir le fichier [LICENSE](LICENSE) pour plus de détails.
