/**
 * Module OBS autonome - Version nettoyée
 * Responsabilité : Connexion WebSocket et orchestration des services OBS
 */

import { Injectable, OnModuleInit, OnModuleDestroy } from '@nestjs/common';
import OBSWebSocket, { EventSubscription } from 'obs-websocket-js';
import { BaseModule } from '../core/base/base-module';
import { obsConfig, modulesConfig } from '../config';
import { OBSObserverService } from './obs-observer.service';
import { OBSControllerService } from './obs-controller.service';

@Injectable()
export class OBSModuleService extends BaseModule implements OnModuleInit, OnModuleDestroy {
    private obs: OBSWebSocket;
    private reconnectTimer?: NodeJS.Timeout;
    private pingTimer?: NodeJS.Timeout;
    private isConnecting = false;
    private readonly config = obsConfig();

    // Services spécialisés
    private observer: OBSObserverService;
    private controller: OBSControllerService;

    constructor() {
        super('obs', modulesConfig().obs.enabled);
        this.obs = new OBSWebSocket();
        this.observer = new OBSObserverService();
        this.controller = new OBSControllerService();
        this.setupServices();
        this.log('OBS module created', { enabled: this.enabled });
    }

    private setupServices(): void {
        // Configurer le callback pour les événements de l'observer
        this.observer.setEventCallback((type: string, data: any) => {
            // Émettre l'événement vers les autres modules
            this.emitEvent(type, data);
        });

        // Configurer les listeners de connexion OBS
        this.setupConnectionListeners();
    }

    async onModuleInit() {
        if (this.enabled) {
            await this.start();
        } else {
            this.log('Module disabled - not starting');
        }
    }

    async onModuleDestroy() {
        await this.stop();
    }

    async start(): Promise<void> {
        if (!this.enabled) {
            this.log('Cannot start - module is disabled');
            return;
        }

        if (this.config.autoConnect) {
            await this.connect();
        }
    }

    async stop(): Promise<void> {
        this.log('Stopping OBS module...');

        if (this.reconnectTimer) {
            clearTimeout(this.reconnectTimer);
            this.reconnectTimer = undefined;
        }

        if (this.pingTimer) {
            clearInterval(this.pingTimer);
            this.pingTimer = undefined;
        }

        if (this.obs.identified) {
            await this.obs.disconnect();
        }

        this.updateConnectionStatus({ connected: false });
        this.log('OBS module stopped');
    }

    // === MÉTHODES PUBLIQUES - CONTRÔLE OBS ===

    /**
     * Changer de scène OBS (exemple de contrôle)
     */
    async changeScene(sceneName: string): Promise<void> {
        if (!this.obs.identified) {
            throw new Error('OBS not connected');
        }

        try {
            await this.controller.changeScene(this.obs, sceneName);
        } catch (error: any) {
            const errorMessage = String(error?.message || 'Unknown error');
            this.handleError(new Error(errorMessage), `Failed to change scene to "${sceneName}"`);
            throw error;
        }
    }

    /**
     * Obtenir la scène actuellement active
     */
    async getCurrentScene(): Promise<string> {
        if (!this.obs.identified) {
            throw new Error('OBS not connected');
        }

        try {
            const response = await this.obs.call('GetCurrentProgramScene');
            return response.currentProgramSceneName;
        } catch (error: any) {
            const errorMessage = String(error?.message || 'Unknown error');
            this.handleError(new Error(errorMessage), 'Failed to get current scene');
            throw error;
        }
    }

    /**
     * Contrôler la visibilité d'une source dans une scène
     * @param sceneName Nom de la scène
     * @param sourceName Nom de la source
     * @param visible True pour visible, false pour masquer
     * @param silent Ne pas logger l'opération (pour mode opacité)
     */
    async setSourceVisibility(sceneName: string, sourceName: string, visible: boolean, silent: boolean = false): Promise<void> {
        if (!this.obs.identified) {
            throw new Error('OBS not connected');
        }

        try {
            await this.controller.setSourceVisibility(this.obs, sceneName, sourceName, visible, silent);
        } catch (error: any) {
            const errorMessage = String(error?.message || 'Unknown error');
            this.handleError(new Error(errorMessage), `Failed to set visibility of "${sourceName}" in "${sceneName}"`);
            throw error;
        }
    }

    /**
     * Masquer toutes les sources spécifiées dans une scène
     * @param sceneName Nom de la scène
     * @param sourceNames Liste des sources à masquer
     * @param exceptSource Source à ne pas masquer (optionnel)
     */
    async hideAllSources(sceneName: string, sourceNames: string[], exceptSource?: string): Promise<void> {
        if (!this.obs.identified) {
            throw new Error('OBS not connected');
        }

        try {
            await this.controller.hideAllSources(this.obs, sceneName, sourceNames, exceptSource);
        } catch (error: any) {
            const errorMessage = String(error?.message || 'Unknown error');
            this.handleError(new Error(errorMessage), `Failed to hide sources in "${sceneName}"`);
            throw error;
        }
    }

    /**
     * Contrôler l'opacité d'une source via un filtre
     * @param sourceName Nom de la source
     * @param opacity Opacité (0-1)
     * @param filterName Nom du filtre (par défaut: "Opacity")
     * @param silent Ne pas logger l'opération
     */
    async setSourceOpacity(sourceName: string, opacity: number, filterName: string = 'Opacity', silent: boolean = false): Promise<void> {
        if (!this.obs.identified) {
            throw new Error('OBS not connected');
        }

        try {
            await this.controller.setSourceOpacity(this.obs, sourceName, opacity, filterName, silent);
        } catch (error: any) {
            const errorMessage = String(error?.message || 'Unknown error');
            this.handleError(new Error(errorMessage), `Failed to set opacity of "${sourceName}"`);
            throw error;
        }
    }

    /**
     * Vérifier si un filtre existe sur une source
     * @param sourceName Nom de la source
     * @param filterName Nom du filtre
     */
    async hasSourceFilter(sourceName: string, filterName: string): Promise<boolean> {
        if (!this.obs.identified) {
            throw new Error('OBS not connected');
        }

        try {
            return await this.controller.hasSourceFilter(this.obs, sourceName, filterName);
        } catch (error: any) {
            const errorMessage = String(error?.message || 'Unknown error');
            this.handleError(new Error(errorMessage), `Failed to check filter on "${sourceName}"`);
            throw error;
        }
    }

    /**
     * Créer un filtre d'opacité sur une source
     * @param sourceName Nom de la source
     * @param filterName Nom du filtre (par défaut: "Opacity")
     */
    async createOpacityFilter(sourceName: string, filterName: string = 'Opacity'): Promise<void> {
        if (!this.obs.identified) {
            throw new Error('OBS not connected');
        }

        try {
            await this.controller.createOpacityFilter(this.obs, sourceName, filterName);
        } catch (error: any) {
            const errorMessage = String(error?.message || 'Unknown error');
            this.handleError(new Error(errorMessage), `Failed to create opacity filter on "${sourceName}"`);
            throw error;
        }
    }

    // === MÉTHODES PRIVÉES ===

    private setupConnectionListeners(): void {
        this.obs.on('ConnectionOpened', () => {
            this.log('WebSocket connection opened');
        });

        this.obs.on('ConnectionClosed', () => {
            this.log('WebSocket connection closed');
            this.updateConnectionStatus({ connected: false });
            this.scheduleReconnect();
        });

        this.obs.on('ConnectionError', (error) => {
            this.log('WebSocket connection error:', error.message);
            this.updateConnectionStatus({ connected: false, error: error.message });
        });
    }

    private async connect(): Promise<void> {
        if (this.isConnecting || this.obs.identified) {
            return;
        }

        this.isConnecting = true;

        try {
            this.log(`Connecting to OBS at ${this.config.network.host}:${this.config.network.port}...`);

            const eventSubs = EventSubscription.General | EventSubscription.InputVolumeMeters | EventSubscription.Scenes;

            const url = `ws://${this.config.network.host}:${this.config.network.port}`;
            const connectionInfo = await this.obs.connect(url, this.config.network.password, { rpcVersion: 1, eventSubscriptions: eventSubs });

            this.log(`Connected to OBS WebSocket v${connectionInfo.obsWebSocketVersion} (RPC v${connectionInfo.negotiatedRpcVersion})`);

            this.updateConnectionStatus({ connected: true });
            this.startPing();

            // Configurer les listeners d'événements
            this.observer.setupEventListeners(this.obs);
        } catch (error: any) {
            const errorMessage = String(error?.message || 'Unknown error');
            this.handleError(new Error(errorMessage), 'Failed to connect to OBS');
            this.scheduleReconnect();
        } finally {
            this.isConnecting = false;
        }
    }

    private startPing(): void {
        this.pingTimer = setInterval(() => {
            if (this.obs.identified) {
                this.obs.call('GetVersion').catch(() => {
                    this.log('Ping failed - connection may be lost');
                });
            }
        }, this.config.timing.pingInterval);
    }

    private scheduleReconnect(): void {
        if (this.reconnectTimer) {
            clearTimeout(this.reconnectTimer);
        }

        this.reconnectTimer = setTimeout(() => {
            this.log('Attempting to reconnect...');
            void this.connect();
        }, this.config.timing.reconnectInterval);
    }

    // === MÉTHODES PUBLIQUES - CONFIGURATION OBSERVER ===

    /**
     * Configurer les sources audio à écouter
     * Utilisé par les links pour spécifier quelles sources surveiller
     */
    configureAudioSources(sources: { inputName: string }[]): void {
        this.observer.setAudioSources(sources);
    }

    /**
     * Configurer les options de debug audio
     * Utilisé par les links pour activer le debug détaillé
     */
    configureAudioDebug(config: { enabled: boolean; logThresholds: boolean; intervalMs: number }): void {
        this.observer.setDebugConfig(config);
    }
}
