/**
 * Export centralisé de toutes les configurations
 * Chargement direct des configurations locales
 */

import type { GabinConfig } from '../gabin/gabin.types';
import type { CompanionConfig } from '../companion/companion.types';
import type { OBSConfig } from '../obs/obs.types';
import type { WinmediaConfig } from '../winmedia/winmedia.types';
import type { LowerThirdConfig } from '../lowerthird/lowerthird.types';

// Types pour les configurations qui n'ont pas de module dédié
export interface ModulesConfig {
    gabin: {
        enabled: boolean;
        description: string;
    };
    obs: {
        enabled: boolean;
        description: string;
    };
    companion: {
        enabled: boolean;
        description: string;
    };
    winmedia: {
        enabled: boolean;
        description: string;
    };
}

export interface MicToCameraMapping {
    micName: string;
    cameraSource: string;
}

export interface AudioToCameraConfig {
    enabled: boolean;
    targetScene: string;
    audioThresholdDb: number;
    activationDelayMs: number;
    cameraMinHoldMs: number;
    micToCameraMapping: MicToCameraMapping[];
    forceFullscreenSources: string[]; // Sources audio qui forcent le mode fullscreen
    fallbackBehavior: {
        hideAllWhenInactive: boolean;
    };
    fullscreenMode: {
        enabled: boolean;
        minActiveMics: number;
    };
    cameraTransition: {
        useOpacityFilter: boolean;
        filterName: string;
    };
    audioStabilization: {
        enabled: boolean;
        fluctuationWindowMs: number;
        minStableDurationMs: number;
    };
    debug: {
        verbose: boolean;
        logAudioLevels: boolean;
        logAudioThresholds: boolean;
        audioLogIntervalMs: number;
    };
}

export interface DjAutoCameraConfig {
    enabled: boolean;
    targetScene: string;
    cameraSources: string[];
    timing: {
        minSwitchIntervalMs: number;
        maxSwitchIntervalMs: number;
    };
    intro: {
        enabled: boolean;
        mainCamera: string;
        durationMs: number;
    };
    sceneDetection: {
        enabled: boolean;
        onlyWhenSceneActive: boolean;
        ghostScenes: string[];
    };
    cameraTransition: {
        useOpacityFilter: boolean;
        filterName: string;
    };
    debug: {
        verbose: boolean;
        logSwitches: boolean;
    };
}

// Chargement direct des configurations personnalisées
function loadGabinCustomConfig(): GabinConfig {
    try {
        // eslint-disable-next-line @typescript-eslint/no-require-imports
        const customModule = require('./custom/gabin.custom') as { gabinCustomConfig: GabinConfig };
        return customModule.gabinCustomConfig;
    } catch (error) {
        throw new Error(`Failed to load gabin.custom.ts: ${error}`);
    }
}

function loadCompanionCustomConfig(): CompanionConfig {
    try {
        // eslint-disable-next-line @typescript-eslint/no-require-imports
        const customModule = require('./custom/companion.custom') as { companionCustomConfig: CompanionConfig };
        return customModule.companionCustomConfig;
    } catch (error) {
        throw new Error(`Failed to load companion.custom.ts: ${error}`);
    }
}

function loadOBSCustomConfig(): OBSConfig {
    try {
        // eslint-disable-next-line @typescript-eslint/no-require-imports
        const customModule = require('./custom/obs.custom') as { obsCustomConfig: OBSConfig };
        return customModule.obsCustomConfig;
    } catch (error) {
        throw new Error(`Failed to load obs.custom.ts: ${error}`);
    }
}

function loadWinmediaCustomConfig(): WinmediaConfig {
    try {
        // eslint-disable-next-line @typescript-eslint/no-require-imports
        const customModule = require('./custom/winmedia.custom') as { winmediaCustomConfig: WinmediaConfig };
        return customModule.winmediaCustomConfig;
    } catch (error) {
        throw new Error(`Failed to load winmedia.custom.ts: ${error}`);
    }
}

function loadModulesCustomConfig(): ModulesConfig {
    try {
        // eslint-disable-next-line @typescript-eslint/no-require-imports
        const customModule = require('./custom/modules.custom') as { modulesCustomConfig: ModulesConfig };
        return customModule.modulesCustomConfig;
    } catch (error) {
        throw new Error(`Failed to load modules.custom.ts: ${error}`);
    }
}

function loadAudioToCameraCustomConfig(): AudioToCameraConfig {
    try {
        // eslint-disable-next-line @typescript-eslint/no-require-imports
        const customModule = require('./custom/audio-to-camera.custom') as { audioToCameraCustomConfig: AudioToCameraConfig };
        return customModule.audioToCameraCustomConfig;
    } catch (error) {
        throw new Error(`Failed to load audio-to-camera.custom.ts: ${error}`);
    }
}

function loadDjAutoCameraCustomConfig(): DjAutoCameraConfig {
    try {
        // eslint-disable-next-line @typescript-eslint/no-require-imports
        const customModule = require('./custom/dj-auto-camera.custom') as { djAutoCameraCustomConfig: DjAutoCameraConfig };
        return customModule.djAutoCameraCustomConfig;
    } catch (error) {
        throw new Error(`Failed to load dj-auto-camera.custom.ts: ${error}`);
    }
}

function loadLowerThirdCustomConfig(): LowerThirdConfig {
    try {
        // eslint-disable-next-line @typescript-eslint/no-require-imports
        const customModule = require('./custom/lowerthird.custom') as { lowerThirdCustomConfig: LowerThirdConfig };
        return customModule.lowerThirdCustomConfig;
    } catch (error) {
        throw new Error(`Failed to load lowerthird.custom.ts: ${error}`);
    }
}

// Export des configurations locales (chargement différé)
let _gabinConfig: GabinConfig | undefined;
let _companionConfig: CompanionConfig | undefined;
let _obsConfig: OBSConfig | undefined;
let _winmediaConfig: WinmediaConfig | undefined;
let _modulesConfig: ModulesConfig | undefined;
let _audioToCameraConfig: AudioToCameraConfig | undefined;
let _djAutoCameraConfig: DjAutoCameraConfig | undefined;
let _lowerThirdConfig: LowerThirdConfig | undefined;

export const gabinConfig = (): GabinConfig => {
    if (!_gabinConfig) {
        _gabinConfig = loadGabinCustomConfig();
    }
    return _gabinConfig;
};

export const companionConfig = (): CompanionConfig => {
    if (!_companionConfig) {
        _companionConfig = loadCompanionCustomConfig();
    }
    return _companionConfig;
};

export const obsConfig = (): OBSConfig => {
    if (!_obsConfig) {
        _obsConfig = loadOBSCustomConfig();
    }
    return _obsConfig;
};

export const winmediaConfig = (): WinmediaConfig => {
    if (!_winmediaConfig) {
        _winmediaConfig = loadWinmediaCustomConfig();
    }
    return _winmediaConfig;
};

export const modulesConfig = (): ModulesConfig => {
    if (!_modulesConfig) {
        _modulesConfig = loadModulesCustomConfig();
    }
    return _modulesConfig;
};

export const audioToCameraConfig = (): AudioToCameraConfig => {
    if (!_audioToCameraConfig) {
        _audioToCameraConfig = loadAudioToCameraCustomConfig();
    }
    return _audioToCameraConfig;
};

export const djAutoCameraConfig = (): DjAutoCameraConfig => {
    if (!_djAutoCameraConfig) {
        _djAutoCameraConfig = loadDjAutoCameraCustomConfig();
    }
    return _djAutoCameraConfig;
};

export const lowerThirdConfig = (): LowerThirdConfig => {
    if (!_lowerThirdConfig) {
        _lowerThirdConfig = loadLowerThirdCustomConfig();
    }
    return _lowerThirdConfig;
};

// Export de la configuration app (inchangée)
export { appConfig, type AppConfig, type HttpConfig } from './app.config';
