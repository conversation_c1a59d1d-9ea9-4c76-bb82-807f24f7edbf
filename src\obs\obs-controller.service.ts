/**
 * Service de contrôle OBS
 * Responsabilité : Envoyer des commandes vers OBS
 */

import { Injectable } from '@nestjs/common';
import { OBSWebSocket } from 'obs-websocket-js';

@Injectable()
export class OBSControllerService {
    constructor() {}

    /**
     * Changer de scène OBS
     * @param obs Instance OBS WebSocket connectée
     * @param sceneName Nom de la scène à activer
     */
    async changeScene(obs: OBSWebSocket, sceneName: string): Promise<void> {
        if (!obs.identified) {
            throw new Error('OBS not connected');
        }

        try {
            await obs.call('SetCurrentProgramScene', { sceneName });
            console.log(`[obs-controller] Scene changed to: ${sceneName}`);
        } catch (error: any) {
            console.error(`[obs-controller] Failed to change scene to "${sceneName}":`, error.message);
            throw error;
        }
    }

    /**
     * Contrôler la visibilité d'une source dans une scène
     * @param obs Instance OBS WebSocket connectée
     * @param sceneName Nom de la scène
     * @param sourceName Nom de la source
     * @param visible True pour rendre visible, false pour masquer
     * @param silent Ne pas logger l'opération (pour mode opacité)
     */
    async setSourceVisibility(obs: OBSWebSocket, sceneName: string, sourceName: string, visible: boolean, silent: boolean = false): Promise<void> {
        if (!obs.identified) {
            throw new Error('OBS not connected');
        }

        try {
            await obs.call('SetSceneItemEnabled', {
                sceneName,
                sceneItemId: await this.getSceneItemId(obs, sceneName, sourceName),
                sceneItemEnabled: visible,
            });

            // ✅ AMÉLIORATION : Log conditionnel selon le mode
            if (!silent) {
                console.log(`[obs-controller] Source "${sourceName}" in scene "${sceneName}" set to ${visible ? 'visible' : 'hidden'}`);
            }
        } catch (error: any) {
            console.error(`[obs-controller] Failed to set visibility of "${sourceName}" in "${sceneName}":`, String(error?.message || 'Unknown error'));
            throw error;
        }
    }

    /**
     * Masquer toutes les sources spécifiées dans une scène
     * @param obs Instance OBS WebSocket connectée
     * @param sceneName Nom de la scène
     * @param sourceNames Liste des noms de sources à masquer
     * @param exceptSource Source à ne pas masquer (optionnel)
     */
    async hideAllSources(obs: OBSWebSocket, sceneName: string, sourceNames: string[], exceptSource?: string): Promise<void> {
        if (!obs.identified) {
            throw new Error('OBS not connected');
        }

        // ✅ AMÉLIORATION : Filtrer la source à ne pas masquer
        const sourcesToHide = exceptSource ? sourceNames.filter((sourceName) => sourceName !== exceptSource) : sourceNames;

        const promises = sourcesToHide.map((sourceName) =>
            this.setSourceVisibility(obs, sceneName, sourceName, false).catch((error) => {
                console.warn(`[obs-controller] Failed to hide source "${sourceName}":`, String(error?.message || 'Unknown error'));
            }),
        );

        await Promise.allSettled(promises);
        const hiddenCount = sourcesToHide.length;
        const exceptMsg = exceptSource ? ` (except ${exceptSource})` : '';
        console.log(`[obs-controller] Attempted to hide ${hiddenCount} sources in scene "${sceneName}"${exceptMsg}`);
    }

    /**
     * Obtenir l'ID d'un élément de scène
     * @param obs Instance OBS WebSocket connectée
     * @param sceneName Nom de la scène
     * @param sourceName Nom de la source
     * @returns ID de l'élément de scène
     */
    private async getSceneItemId(obs: OBSWebSocket, sceneName: string, sourceName: string): Promise<number> {
        try {
            const response = await obs.call('GetSceneItemId', {
                sceneName,
                sourceName,
            });
            return response.sceneItemId;
        } catch (error: any) {
            console.error(`[obs-controller] Failed to get scene item ID for "${sourceName}" in "${sceneName}":`, String(error?.message || 'Unknown error'));
            throw error;
        }
    }

    /**
     * Contrôler l'opacité d'une source via un filtre
     * @param obs Instance OBS WebSocket connectée
     * @param sourceName Nom de la source
     * @param opacity Opacité (0-1)
     * @param filterName Nom du filtre d'opacité (par défaut: "Opacity")
     * @param silent Ne pas logger l'opération
     */
    async setSourceOpacity(obs: OBSWebSocket, sourceName: string, opacity: number, filterName: string = 'Opacity', silent: boolean = false): Promise<void> {
        if (!obs.identified) {
            throw new Error('OBS not connected');
        }

        try {
            await obs.call('SetSourceFilterSettings', {
                sourceName,
                filterName,
                filterSettings: {
                    opacity: Math.max(0, Math.min(1, opacity)), // ✅ Clamp entre 0 et 1
                },
            });
            if (!silent) {
                console.log(`[obs-controller] Source "${sourceName}" opacity set to ${Math.round(opacity * 100)}% (filter: ${filterName})`);
            }
        } catch (error: any) {
            console.error(`[obs-controller] Failed to set opacity of "${sourceName}":`, String(error?.message || 'Unknown error'));
            throw error;
        }
    }

    /**
     * Vérifier si un filtre existe sur une source
     * @param obs Instance OBS WebSocket connectée
     * @param sourceName Nom de la source
     * @param filterName Nom du filtre
     */
    async hasSourceFilter(obs: OBSWebSocket, sourceName: string, filterName: string): Promise<boolean> {
        if (!obs.identified) {
            throw new Error('OBS not connected');
        }

        try {
            await obs.call('GetSourceFilter', {
                sourceName,
                filterName,
            });
            return true;
        } catch (error: any) {
            // Si le filtre n'existe pas, OBS retourne une erreur
            return false;
        }
    }

    /**
     * Créer un filtre d'opacité sur une source
     * @param obs Instance OBS WebSocket connectée
     * @param sourceName Nom de la source
     * @param filterName Nom du filtre (par défaut: "Opacity")
     */
    async createOpacityFilter(obs: OBSWebSocket, sourceName: string, filterName: string = 'Opacity'): Promise<void> {
        if (!obs.identified) {
            throw new Error('OBS not connected');
        }

        try {
            await obs.call('CreateSourceFilter', {
                sourceName,
                filterName,
                filterKind: 'opacity_filter',
                filterSettings: {
                    opacity: 1, // ✅ Commencer visible (1 = 100%)
                },
            });
            console.log(`[obs-controller] Created opacity filter "${filterName}" on source "${sourceName}"`);
        } catch (error: any) {
            console.error(`[obs-controller] Failed to create opacity filter on "${sourceName}":`, String(error?.message || 'Unknown error'));
            throw error;
        }
    }

    // Ici vous pourrez ajouter d'autres méthodes de contrôle si nécessaire :
    // - setSourceVolume()
    // - setSourceMute()
    // - etc.
}
