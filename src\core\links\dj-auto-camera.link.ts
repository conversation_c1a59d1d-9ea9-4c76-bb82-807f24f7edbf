/**
 * Lien DJ Auto Camera
 *
 * Automation pour les mix DJ qui change automatiquement les caméras de manière aléatoire
 * à intervalles réguliers pour créer un spectacle visuel dynamique.
 *
 * Fonctionnalités :
 * - Changements automatiques de caméras à intervalles aléatoires
 * - Support des transitions par opacité pour éviter les frames anciennes
 * - Configuration flexible des caméras et des timings
 * - Logs détaillés pour le debug
 */

import { IModuleLink, LinkConfig } from '../interfaces/link.interface';
import { IModule } from '../interfaces/module.interface';
import { djAutoCameraConfig } from '../../config';

interface CameraState {
    /** Source caméra actuellement active */
    activeCamera: string | null;
    /** Timer principal pour les changements automatiques */
    switchTimer: NodeJS.Timeout | null;
    /** Indique si l'automation est en cours */
    isRunning: boolean;
    /** Indique si on est en mode intro */
    isInIntroMode: boolean;
    /** Timer pour la fin de l'intro */
    introTimer: NodeJS.Timeout | null;
    /** Indique si la scène cible était active lors de la dernière vérification */
    lastSceneActiveState: boolean;
    /** Indique si on a déjà affiché le message de scène inactive */
    sceneInactiveMessageShown: boolean;
    /** La dernière scène "réelle" (non-transparente) visitée */
    lastRealScene: string | null;
}

export class DjAutoCameraLink implements IModuleLink {
    public readonly name = 'dj-auto-camera';
    public readonly description = 'Changements automatiques de caméras pour les mix DJ';
    public readonly enabled: boolean;

    private obsModule?: IModule;
    private config = djAutoCameraConfig();

    // État interne
    private cameraState: CameraState = {
        activeCamera: null,
        switchTimer: null,
        isRunning: false,
        isInIntroMode: false,
        introTimer: null,
        lastSceneActiveState: true,
        sceneInactiveMessageShown: false,
        lastRealScene: null,
    };

    constructor(linkConfig: LinkConfig) {
        this.enabled = linkConfig.enabled && this.config?.enabled;
    }

    async initialize(modules: Map<string, IModule>): Promise<void> {
        await Promise.resolve();

        if (!this.enabled) {
            console.log('[DjAutoCameraLink] Link disabled - skipping initialization');
            return;
        }

        // Récupérer le module OBS
        this.obsModule = modules.get('obs');
        if (!this.obsModule) {
            console.warn('[DjAutoCameraLink] OBS module not found - link will not function');
            return;
        }

        // Valider la configuration
        if (!this.config.cameraSources || this.config.cameraSources.length < 2) {
            console.warn('[DjAutoCameraLink] At least 2 camera sources are required');
            return;
        }

        // Toujours écouter les événements de connexion OBS
        this.setupOBSConnectionListener();

        // Vérifier que OBS est connecté avant de démarrer
        if (!this.isOBSConnected()) {
            console.log('[DjAutoCameraLink] OBS not connected - automation will start when OBS becomes available');
            return;
        }

        // Vérifier la scène actuelle avant de démarrer l'automation
        await this.checkInitialScene();

        console.log(`[DjAutoCameraLink] Initialized with ${this.config.cameraSources.length} camera sources`);
        if (this.config.debug.verbose) {
            console.log('[DjAutoCameraLink] Configuration:', {
                targetScene: this.config.targetScene,
                cameraSources: this.config.cameraSources,
                minInterval: this.config.timing.minSwitchIntervalMs,
                maxInterval: this.config.timing.maxSwitchIntervalMs,
            });
        }
    }

    async cleanup(): Promise<void> {
        await Promise.resolve();

        // Arrêter l'automation
        await this.stopAutomation();

        console.log('[DjAutoCameraLink] Cleaned up');
    }

    // ============================================================================
    // MÉTHODES PUBLIQUES POUR COMPANION
    // ============================================================================

    /**
     * Déclencher l'intro manuellement (appelé depuis Companion)
     */
    public async triggerIntro(): Promise<void> {
        if (!this.enabled || !this.config.intro.enabled) {
            console.log('[DjAutoCameraLink] Intro disabled or link disabled');
            return;
        }

        if (this.cameraState.isInIntroMode) {
            console.log('[DjAutoCameraLink] Intro already in progress');
            return;
        }

        // Vérifier si on est sur la bonne scène avant de déclencher l'intro
        if (this.config.sceneDetection.enabled && !(await this.isOnValidScene())) {
            console.log('[DjAutoCameraLink] Intro can only be triggered when on target scene or ghost scene');
            return;
        }

        console.log(`[${this.getTimestamp()}] [DjAutoCameraLink] 🎬 Intro triggered manually`);

        await this.startIntroMode();
    }

    // ============================================================================
    // VÉRIFICATIONS ET LISTENERS
    // ============================================================================

    /**
     * Vérifier si on est logiquement sur la scène cible
     */
    private async isOnValidScene(): Promise<boolean> {
        if (!this.obsModule) {
            return false;
        }

        try {
            // eslint-disable-next-line
            const currentScene = await (this.obsModule as any).getCurrentScene();

            // Si on est sur une scène fantôme, utiliser la dernière vraie scène
            if (this.config.sceneDetection.ghostScenes.includes(currentScene)) {
                return this.cameraState.lastRealScene === this.config.targetScene;
            }

            // Sinon, vérifier directement la scène actuelle
            return currentScene === this.config.targetScene;
        } catch (error) {
            console.warn('[DjAutoCameraLink] Could not check current scene:', error);
            return false;
        }
    }

    /**
     * Vérifier la scène actuelle à l'initialisation
     */
    private async checkInitialScene(): Promise<void> {
        if (!this.obsModule || !this.config.sceneDetection.enabled) {
            // Si la détection de scène est désactivée, démarrer l'automation
            await this.startAutomation();
            return;
        }

        try {
            // eslint-disable-next-line
            const currentScene = await (this.obsModule as any).getCurrentScene();

            // Si on est sur une scène fantôme, on ne peut pas déterminer la vraie scène
            // Dans ce cas, considérer qu'on n'est pas sur la scène cible
            const isGhostScene = this.config.sceneDetection.ghostScenes.includes(currentScene);

            if (isGhostScene) {
                console.log(`[DjAutoCameraLink] Currently on ghost scene '${currentScene}' - cannot determine logical scene - automation paused`);
                this.cameraState.lastRealScene = null;
                this.cameraState.lastSceneActiveState = false;
                // Ne pas démarrer l'automation
            } else {
                // Scène réelle, mettre à jour l'état
                this.cameraState.lastRealScene = currentScene;
                const isTargetScene = currentScene === this.config.targetScene;
                this.cameraState.lastSceneActiveState = isTargetScene;

                if (isTargetScene) {
                    console.log(`[DjAutoCameraLink] Currently on target scene '${this.config.targetScene}' - starting automation`);
                    await this.startAutomation();
                } else {
                    console.log(`[DjAutoCameraLink] Currently on scene '${currentScene}' (not target '${this.config.targetScene}') - automation paused`);
                    // Ne pas démarrer l'automation, attendre le changement de scène
                }
            }
        } catch (error) {
            console.warn('[DjAutoCameraLink] Could not check initial scene, starting automation anyway:', error);
            // En cas d'erreur, démarrer l'automation par défaut
            await this.startAutomation();
        }
    }

    /**
     * Vérifier si OBS est connecté
     */
    private isOBSConnected(): boolean {
        if (!this.obsModule) return false;

        try {
            // Vérifier l'état de connexion via le status du module
            const status = this.obsModule.getConnectionStatus();
            return status?.connected || false;
        } catch (error) {
            return false;
        }
    }

    /**
     * Écouter les événements de connexion OBS et changements de scène
     */
    private setupOBSConnectionListener(): void {
        if (!this.obsModule) return;

        const eventCallback = (event: any) => {
            if (event.type === 'connection_changed') {
                if (event.data?.connected) {
                    console.log('[DjAutoCameraLink] OBS connected - starting automation');
                    void this.startAutomation();
                } else {
                    console.log('[DjAutoCameraLink] OBS disconnected - stopping automation');
                    void this.stopAutomation();
                }
            } else if (event.type === 'scene_changed' && this.config.sceneDetection.enabled) {
                // Gérer les changements de scène en temps réel
                const newSceneName = event.data?.sceneName;
                if (newSceneName) {
                    void this.handleSceneChange(newSceneName);
                }
            }
        };

        this.obsModule.onEvent(eventCallback);
    }

    /**
     * Gérer les changements de scène en temps réel
     */
    private async handleSceneChange(newSceneName: string): Promise<void> {
        // Si c'est une scène fantôme, l'ignorer complètement
        if (this.config.sceneDetection.ghostScenes.includes(newSceneName)) {
            if (this.config.debug.verbose) {
                console.log(`[DjAutoCameraLink] Ignoring ghost scene '${newSceneName}' - staying on logical scene '${this.cameraState.lastRealScene}'`);
            }
            return;
        }

        // Mettre à jour la vraie scène actuelle
        this.cameraState.lastRealScene = newSceneName;

        const isTargetScene = newSceneName === this.config.targetScene;
        const wasActive = this.cameraState.lastSceneActiveState;

        if (isTargetScene !== wasActive) {
            await this.handleSceneStateChange(isTargetScene);
        }
    }

    /**
     * Gérer les changements d'état de la scène (active/inactive)
     */
    private async handleSceneStateChange(isSceneActive: boolean): Promise<void> {
        const wasActive = this.cameraState.lastSceneActiveState;
        this.cameraState.lastSceneActiveState = isSceneActive;

        if (!isSceneActive && wasActive) {
            // Passage de actif à inactif - ARRÊTER complètement l'automation
            console.log(`[DjAutoCameraLink] Scene '${this.config.targetScene}' became inactive - stopping automation`);
            this.cameraState.sceneInactiveMessageShown = true;

            // Arrêter l'automation
            this.cameraState.isRunning = false;

            // Arrêter le timer de changement de caméra
            if (this.cameraState.switchTimer) {
                clearTimeout(this.cameraState.switchTimer);
                this.cameraState.switchTimer = null;
            }

            // Revenir à la caméra principale quand on quitte la scène
            await this.returnToMainCamera();
        } else if (isSceneActive && !wasActive) {
            // Passage de inactif à actif - REDÉMARRER l'automation
            console.log(`[DjAutoCameraLink] Scene '${this.config.targetScene}' became active - resuming automation`);
            this.cameraState.sceneInactiveMessageShown = false;

            // Remettre l'automation en marche
            this.cameraState.isRunning = true;

            // Retourner à la caméra principale
            await this.returnToMainCamera();

            // Redémarrer l'automation normale
            this.scheduleNextSwitch();
        } else if (isSceneActive) {
            // Reset du flag si on est toujours actif
            this.cameraState.sceneInactiveMessageShown = false;
        }
    }

    /**
     * Retourner à la caméra principale lors de la réactivation de la scène
     */
    private async returnToMainCamera(): Promise<void> {
        if (!this.config.intro.enabled) {
            return; // Pas de caméra principale définie
        }

        const mainCamera = this.config.intro.mainCamera;
        if (!this.config.cameraSources.includes(mainCamera)) {
            return; // Caméra principale non trouvée
        }

        // Activer la caméra principale (avec logs normaux car c'est un événement important)
        await this.activateCamera(mainCamera);

        console.log(`[DjAutoCameraLink] 🏠 Returned to main camera: ${mainCamera}`);
    }

    // ============================================================================
    // GESTION DE L'AUTOMATION
    // ============================================================================

    /**
     * Démarrer l'automation des changements de caméras
     */
    private async startAutomation(): Promise<void> {
        if (this.cameraState.isRunning) {
            return;
        }

        this.cameraState.isRunning = true;

        // Toujours démarrer en mode normal
        // L'intro ne se déclenche que manuellement via Companion
        await this.startNormalMode();

        if (this.config.debug.verbose) {
            console.log(`[${this.getTimestamp()}] [DjAutoCameraLink] 🎬 Automation started`);
        }
    }

    /**
     * Démarrer le mode intro
     */
    private async startIntroMode(): Promise<void> {
        if (this.cameraState.isInIntroMode) {
            return;
        }

        this.cameraState.isInIntroMode = true;

        // Activer la caméra principale
        const mainCamera = this.config.intro.mainCamera;
        if (this.config.cameraSources.includes(mainCamera)) {
            await this.activateCamera(mainCamera);

            if (this.config.debug.verbose) {
                console.log(`[${this.getTimestamp()}] [DjAutoCameraLink] 🎭 Intro mode: ${mainCamera} for ${this.config.intro.durationMs / 1000}s`);
            }
        } else {
            console.warn(`[DjAutoCameraLink] Main camera '${mainCamera}' not found in camera sources`);
            // Fallback vers l'automation normale
            await this.startNormalMode();
            return;
        }

        // Programmer la fin de l'intro
        this.cameraState.introTimer = setTimeout(async () => {
            this.cameraState.introTimer = null;
            await this.endIntroMode();
        }, this.config.intro.durationMs);
    }

    /**
     * Terminer le mode intro et passer à l'automation normale
     */
    private async endIntroMode(): Promise<void> {
        if (!this.cameraState.isInIntroMode) {
            return;
        }

        this.cameraState.isInIntroMode = false;

        if (this.config.debug.verbose) {
            console.log(`[${this.getTimestamp()}] [DjAutoCameraLink] 🎭 Intro mode ended, starting normal automation`);
        }

        // Démarrer l'automation normale
        await this.startNormalMode();
    }

    /**
     * Démarrer le mode automation normale
     */
    private async startNormalMode(): Promise<void> {
        if (this.cameraState.isInIntroMode) {
            return; // Ne pas démarrer si on est encore en intro
        }

        // Choisir une caméra initiale aléatoire
        await this.switchToRandomCamera();

        // Programmer le prochain changement
        this.scheduleNextSwitch();
    }

    /**
     * Arrêter l'automation des changements de caméras
     */
    private async stopAutomation(): Promise<void> {
        this.cameraState.isRunning = false;
        this.cameraState.isInIntroMode = false;

        // Annuler tous les timers en cours
        if (this.cameraState.switchTimer) {
            clearTimeout(this.cameraState.switchTimer);
            this.cameraState.switchTimer = null;
        }

        if (this.cameraState.introTimer) {
            clearTimeout(this.cameraState.introTimer);
            this.cameraState.introTimer = null;
        }

        if (this.config.debug.verbose) {
            console.log(`[${this.getTimestamp()}] [DjAutoCameraLink] ⏹️ Automation stopped`);
        }
    }

    /**
     * Programmer le prochain changement de caméra
     */
    private scheduleNextSwitch(): void {
        if (!this.cameraState.isRunning || this.cameraState.isInIntroMode) {
            return;
        }

        // Ne programmer que si la scène est active (ou si la détection est désactivée)
        if (this.config.sceneDetection.enabled && !this.cameraState.lastSceneActiveState) {
            return; // Ne pas programmer si la scène n'est pas active
        }

        // Calculer un délai aléatoire entre min et max
        const minDelay = this.config.timing.minSwitchIntervalMs;
        const maxDelay = this.config.timing.maxSwitchIntervalMs;
        const randomDelay = Math.floor(Math.random() * (maxDelay - minDelay + 1)) + minDelay;

        if (this.config.debug.verbose) {
            console.log(`[DjAutoCameraLink] ⏰ Next switch in ${Math.round(randomDelay / 1000)}s`);
        }

        // Programmer le changement
        this.cameraState.switchTimer = setTimeout(async () => {
            this.cameraState.switchTimer = null;

            if (this.cameraState.isRunning && !this.cameraState.isInIntroMode) {
                // Vérifier une dernière fois si la scène est toujours active
                if (this.config.sceneDetection.enabled && !this.cameraState.lastSceneActiveState) {
                    return; // Ne pas changer si la scène n'est plus active
                }

                await this.switchToRandomCamera();
                this.scheduleNextSwitch(); // Programmer le suivant
            }
        }, randomDelay);
    }

    // ============================================================================
    // CONTRÔLE DES CAMÉRAS
    // ============================================================================

    /**
     * Changer vers une caméra aléatoire (différente de l'actuelle et optionnellement d'une caméra à exclure)
     */
    private async switchToRandomCamera(excludeCamera?: string | null): Promise<void> {
        if (!this.obsModule || this.config.cameraSources.length < 2) {
            return;
        }

        // Filtrer les caméras disponibles (exclure la caméra actuelle et optionnellement une autre)
        const availableCameras = this.config.cameraSources.filter((camera) => {
            if (camera === this.cameraState.activeCamera) return false;
            if (excludeCamera && camera === excludeCamera) return false;
            return true;
        });

        if (availableCameras.length === 0) {
            // Si aucune caméra disponible, utiliser toutes les caméras sauf l'actuelle
            const fallbackCameras = this.config.cameraSources.filter((camera) => camera !== this.cameraState.activeCamera);
            if (fallbackCameras.length > 0) {
                const randomIndex = Math.floor(Math.random() * fallbackCameras.length);
                const selectedCamera = fallbackCameras[randomIndex];
                await this.activateCamera(selectedCamera);
            }
            return;
        }

        // Choisir une caméra aléatoire
        const randomIndex = Math.floor(Math.random() * availableCameras.length);
        const selectedCamera = availableCameras[randomIndex];

        // Activer la caméra sélectionnée
        await this.activateCamera(selectedCamera);
    }

    /**
     * Activer une caméra spécifique
     */
    private async activateCamera(cameraSource: string): Promise<void> {
        if (!this.obsModule) return;

        try {
            if (this.config.cameraTransition?.useOpacityFilter) {
                // Méthode avec opacité pour des transitions fluides
                await this.activateCameraWithOpacity(cameraSource);
            } else {
                // Méthode classique avec visibilité
                await this.activateCameraWithVisibility(cameraSource);
            }

            // Mettre à jour l'état
            this.cameraState.activeCamera = cameraSource;

            if (this.config.debug.logSwitches) {
                console.log(`[${this.getTimestamp()}] [DjAutoCameraLink] 📹 ${cameraSource} activated`);
            }
        } catch (error) {
            console.error(`[DjAutoCameraLink] Failed to activate camera ${cameraSource}:`, error);
        }
    }

    /**
     * Activer une caméra en utilisant l'opacité
     */
    private async activateCameraWithOpacity(cameraSource: string): Promise<void> {
        if (!this.obsModule) return;

        const filterName = this.config.cameraTransition?.filterName || 'Opacity';

        try {
            // S'assurer que toutes les caméras ont un filtre d'opacité
            await this.ensureOpacityFilters(filterName);

            // Rendre toutes les caméras visibles mais transparentes (mode silencieux)
            for (const camera of this.config.cameraSources) {
                // eslint-disable-next-line
                await (this.obsModule as any).setSourceVisibility(this.config.targetScene, camera, true, true);
            }

            // Mettre toutes les caméras à opacité 0 (sauf celle à activer) - mode silencieux
            const otherCameras = this.config.cameraSources.filter((cam) => cam !== cameraSource);
            for (const camera of otherCameras) {
                // eslint-disable-next-line
                await (this.obsModule as any).setSourceOpacity(camera, 0, filterName, true);
            }

            // Activer la caméra cible avec opacité 1 (100%) - mode silencieux
            // eslint-disable-next-line
            await (this.obsModule as any).setSourceOpacity(cameraSource, 1, filterName, true);
        } catch (error) {
            console.error('[DjAutoCameraLink] Failed to activate camera with opacity:', error);
            // Fallback vers la méthode classique
            await this.activateCameraWithVisibility(cameraSource);
        }
    }

    /**
     * Activer une caméra en utilisant la visibilité classique
     */
    private async activateCameraWithVisibility(cameraSource: string): Promise<void> {
        if (!this.obsModule) return;

        // Activer la nouvelle caméra
        // eslint-disable-next-line
        await (this.obsModule as any).setSourceVisibility(this.config.targetScene, cameraSource, true);

        // Masquer toutes les autres caméras
        // eslint-disable-next-line
        await (this.obsModule as any).hideAllSources(this.config.targetScene, this.config.cameraSources, cameraSource);
    }

    /**
     * S'assurer que toutes les caméras ont un filtre d'opacité
     */
    private async ensureOpacityFilters(filterName: string): Promise<void> {
        if (!this.obsModule) return;

        for (const camera of this.config.cameraSources) {
            try {
                // eslint-disable-next-line
                const hasFilter = await (this.obsModule as any).hasSourceFilter(camera, filterName);
                if (!hasFilter) {
                    // eslint-disable-next-line
                    await (this.obsModule as any).createOpacityFilter(camera, filterName);
                    if (this.config.debug.verbose) {
                        console.log(`[DjAutoCameraLink] Created opacity filter for ${camera}`);
                    }
                }
            } catch (error) {
                console.warn(`[DjAutoCameraLink] Could not ensure opacity filter for ${camera}:`, error);
            }
        }
    }

    // ============================================================================
    // UTILITAIRES
    // ============================================================================

    // ============================================================================
    // UTILITAIRES
    // ============================================================================

    /**
     * Créer un timestamp formaté pour les logs
     */
    private getTimestamp(): string {
        const now = new Date();
        return now.toLocaleTimeString('fr-FR', {
            hour12: false,
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit',
            fractionalSecondDigits: 3,
        });
    }
}
