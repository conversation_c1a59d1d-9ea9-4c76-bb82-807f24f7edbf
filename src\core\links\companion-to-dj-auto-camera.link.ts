/**
 * Lien Companion → DJ Auto Camera
 * Transmet les commandes de Companion vers le DJ Auto Camera
 */

import { IModuleLink, LinkConfig } from '../interfaces/link.interface';
import { IModule, IControlModule } from '../interfaces/module.interface';
import { DjAutoCameraLink } from './dj-auto-camera.link';

export class CompanionToDjAutoCameraLink implements IModuleLink {
    public readonly name = 'companion-to-dj-auto-camera';
    public readonly description = 'Transmet les commandes de Companion vers le DJ Auto Camera';
    public readonly enabled: boolean;

    private companionModule?: IControlModule;
    private djAutoCameraLink?: DjAutoCameraLink;
    private cleanupCallbacks: Array<() => void> = [];

    constructor(config: LinkConfig) {
        this.enabled = config.enabled;
    }

    async initialize(modules: Map<string, IModule>): Promise<void> {
        if (!this.enabled) {
            return;
        }

        // Récupérer le module Companion
        this.companionModule = modules.get('companion') as IControlModule;

        if (!this.companionModule) {
            console.log(`[${this.name}] Companion module not available - link disabled`);
            return;
        }

        console.log(`[${this.name}] Initializing link between Companion and DJ Auto Camera`);

        // S'abonner aux événements de Companion
        this.setupCompanionListeners();

        console.log(`[${this.name}] Link initialized successfully`);
    }

    async cleanup(): Promise<void> {
        // Exécuter tous les callbacks de nettoyage
        this.cleanupCallbacks.forEach((callback) => callback());
        this.cleanupCallbacks = [];

        console.log(`[${this.name}] Cleaned up`);
    }

    /**
     * Définir la référence vers le DJ Auto Camera Link
     * Cette méthode sera appelée par le LinkManager après l'initialisation de tous les links
     */
    public setDjAutoCameraLink(djAutoCameraLink: DjAutoCameraLink): void {
        this.djAutoCameraLink = djAutoCameraLink;
    }

    /**
     * Configurer les écouteurs d'événements de Companion
     */
    private setupCompanionListeners(): void {
        if (!this.companionModule) {
            return;
        }

        // Écouter les actions de Companion
        const companionEventCallback = (event: any) => {
            if (event.type === 'companion_action') {
                this.handleCompanionAction(event.data);
            }
        };

        this.companionModule.onEvent(companionEventCallback);
        this.cleanupCallbacks.push(() => {
            // Note: Dans une implémentation complète, il faudrait pouvoir se désabonner
            // Pour l'instant, on se contente de vider le callback
        });

        console.log(`[${this.name}] Companion event listeners configured`);
    }

    /**
     * Traiter une action reçue de Companion
     */
    private async handleCompanionAction(actionData: any): Promise<void> {
        const { actionType } = actionData;

        // Ne traiter que les actions DJ Auto Camera
        if (actionType === 'dj-auto-camera/intro') {
            console.log(`[${this.name}] Processing DJ Auto Camera intro trigger`);

            if (!this.djAutoCameraLink) {
                console.warn(`[${this.name}] DJ Auto Camera link not available`);
                return;
            }

            try {
                await this.djAutoCameraLink.triggerIntro();
            } catch (error) {
                console.error(`[${this.name}] Error triggering DJ Auto Camera intro:`, error);
            }
        }
        // Ignorer silencieusement les autres actions
    }
}
